import traceback
from typing import Dict, List

from pydantic import BaseModel, Field

from chat.chat_utils.summary_agent import extract_summary_by_agent

from app.ai_models.anthropic_provider import CitationObject, TextObject, StopEvent
from app.middlewares.exceptions import raise_exception
from app.middlewares.logging_utils import app_logger as logger
from chat.context_builder import build_context_for_anthropic
from chat_history.services.encrypt_data_manager import Chat<PERSON><PERSON>oryManager
from app.redis_db.redis_chat_manager import RedisChatManager
from app.utils.analytics import create_document
from app.utils.pre_chat_dto import ChatChunkContext, SearchTypeEnum, ChatSettings, Filters
from app.utils.datetime_utils import timestamp_now_il
from utils.conversation_manager import RoleEnum, ConversationEnum, ConversationItem, ConversationManager, CitationIndex
from chat.chat_utils.chat_dto import ChatClaudeRequest, ChatAnalytics


class ChatObject(BaseModel):
    chat_id: str
    chat_settings: ChatSettings = Field(default_factory=ChatSettings)
    filters: Filters = Field(default_factory=Filters)


def extract_chat_chunk_contexts(mapper_cid_id: Dict[str, dict]) -> List[ChatChunkContext]:
    result = []
    try:
        for value in mapper_cid_id.values():
            if "cid_offsets" in value and value["cid_offsets"]:
                result.extend([
                    ChatChunkContext(
                        docId=offset["cid"],
                        type="txt_id",
                        cited=offset.get("cited", False),
                        master_chunk=value.get("master_chunk", False)
                    )
                    for offset in value["cid_offsets"]
                ])
            else:
                result.append(
                    ChatChunkContext(
                        docId=str(value.get("txt_id")),
                        type="txt_id",
                        cited=value.get("cited", False),
                        master_chunk=value.get("master_chunk", False)
                    )
                )
    except Exception as e:
        logger.error(f"Error extracting ChunkContext from mapper_cid_id: {e} {traceback.format_exc()}")
    return result


async def chat_claude(request: ChatClaudeRequest):
    user_id = request.user_id
    chat_id = request.chat_id
    redis_pool = request.redis_pool
    ai_provider = request.ai_provider
    mongo_client = request.mongo_client

    try:
        # Load chat data from Redis using RedisChatManager
        _, chat_data = await RedisChatManager(redis_pool).load_chat_from_redis(chat_id, user_id, "")
        if chat_data is None:
            raise ValueError("EXTRACT_FROM_REDIS_ERROR")

        # Extract data from chat_data
        conversation = chat_data.conversation
        search_results = chat_data.data.data_raw
        final_answer, citation_text = '', ''
        citation_number = chat_data.chat_settings.citation_count
        search_type = SearchTypeEnum.from_value(chat_data.chat_settings.search_type)
        session_index = chat_data.chat_settings.session_index
        domain = chat_data.domain
        citation_with_offset = True if domain == "verdict" and 'chunks_document' == chat_data.data.type else False
        conversation_data, mapper_cid_id = build_context_for_anthropic(chat_data, search_type)
        chat_analytics = ChatAnalytics(user_id=user_id,
                                       chat_id=chat_id,
                                       query_index=session_index
                                       )
        stream = ai_provider.provider_choose('chat_anthropic').stream_generate_message(
            conversation_data, chat_data.model_settings.model, mapper_cid_id=mapper_cid_id,
            citation_number=citation_number,
            search_results=search_results,
            master_chunk=chat_data.data.master_chunks, citation_with_offset=citation_with_offset
        )

        # Initialize variables for processing the stream
        citation_list, citation_to_display = [], []
        chat_analytics.ttfb = timestamp_now_il()
        async for completion in stream:
            if isinstance(completion, TextObject):
                if "##" in completion.text:
                    final_answer += "\n"
                    for chunk in citation_to_display:
                        citation_number = chunk.get("citation_number", 0)
                        if search_type == SearchTypeEnum.full_db:
                            final_answer += f"[-{citation_number}-]"
                        yield {"type": "chunk", "chunk": chunk}
                    citation_to_display = []
                final_answer += completion.text
                yield completion

            elif isinstance(completion, CitationObject):
                completion = completion.model_dump()
                citation_to_display.append(completion)
                citation_list.append(completion)

            elif isinstance(completion, StopEvent):
                if citation_to_display:
                    for chunk in citation_to_display:
                        if search_type == SearchTypeEnum.full_db:
                            final_answer += f"[-{chunk.get('citation_number', 0)}-]"
                        yield {"type": "chunk", "chunk": chunk}
                chat_analytics.ttlb = timestamp_now_il()
                chat_analytics.response_length = len(final_answer)
                chat_analytics.input_tokens = completion.input_tokens
                chat_analytics.output_tokens = completion.output_tokens
                chat_analytics.model = completion.model_version
                print(f"stop_event: {completion}")

        multiple_answer = False
        include_title = True if session_index == 1 else False

        if search_type != SearchTypeEnum.trainer:
            chat_analytics.chunks_context = extract_chat_chunk_contexts(mapper_cid_id)
            summary, topic = await extract_summary_by_agent(chat_data.query, final_answer, include_title=include_title)
            if summary:
                chat_data.summaries.append(summary)
        else:
            multiple_answer = True
            topic = chat_data.chat_settings.title if chat_data.chat_settings.title else ""

        if include_title and topic:
            chat_data.chat_settings.title = topic
            chat_analytics.title = topic
            chat_object = ChatObject(
                chat_id=chat_id,
                filters=chat_data.filters.model_dump(),
                chat_settings=chat_data.chat_settings.model_dump()
            ).model_dump(mode="json")

            yield {"type": "chat_object", "chat_object": chat_object}
        else:
            chat_analytics.title = chat_data.chat_settings.title
        conversation_manager = ConversationManager(conversation)
        conversation_manager.add(ConversationItem(role=RoleEnum.assistant, content=final_answer,
                                                  citations_index=CitationIndex(
                                                      start=chat_data.chat_settings.citation_count,
                                                      end=chat_data.chat_settings.citation_count + len(
                                                          citation_list) - 1),
                                                  action=ConversationEnum.BOTH.value,
                                                  entry_type="answer"), multiple_answer=multiple_answer)

        # Create the base data for the RedisSchema
        chat_data.conversation = conversation_manager.conversation
        chat_data.answer = final_answer
        chat_data.citations.extend(citation_list)
        chat_data.chat_settings.citation_count += len(citation_list)

        # Save to Redis using RedisChatManager
        success = await RedisChatManager(redis_pool).save_chat_in_redis(chat_data)

        try:
            from api.dependencies.encryption_services import (
                get_encryption_manager,
                get_user_key_manager,
                get_chat_history_manager
            )

            # Manually resolve dependencies (since we're not in a FastAPI endpoint)
            encryption_manager = get_encryption_manager()
            user_key_manager = get_user_key_manager(encryption_manager)
            history_manager = ChatHistoryManager(
                encryption_manager=encryption_manager,
                mongo_client=mongo_client,
                user_key_manager=user_key_manager
            )
            # Store chat with encryption
            chat_data = await history_manager.store_chat(chat_data)
            print(str(chat_data))
        except Exception as e:
            logger.error(f"Failed to store chat in MongoDB: {e}", exc_info=True)
        # If saving to Redis was successful, save the chat analytics

        if not success:
            logger.warning(f"Failed to save chat {chat_id} to Redis")
            raise ValueError("SAVE_IN_REDIS_ERROR")

        try:
            create_document("chat_analytics", chat_analytics.model_dump())
        except Exception as e:
            logger.warning(f"ChatAnalytics error - {e}")


    except Exception as e:
        logger.error(f'Failed to chat for chat_id: {chat_id}, error: {e}', exc_info=True)

        raise_exception(e)
