from typing import Any

from datetime import datetime
from pydantic import BaseModel, Field
from typing import List, Optional

from utils.pre_chat_dto import ChatChunkContext
from utils.datetime_utils import timestamp_now_il


class ChatClaudeRequest(BaseModel):
    user_id: str
    chat_id: str
    has_nearests: bool
    redis_pool: Any
    ai_provider: Any
    mongo_client: Any


class ChatAnalytics(BaseModel):
    user_id: str
    chat_id: str
    query_index: int
    timestamp: datetime = Field(default_factory=timestamp_now_il)
    chunks_context: List[ChatChunkContext] = []
    ttfb: Optional[datetime] = None  # Time To First Byte
    ttlb: Optional[datetime] = None  # Time To Last Byte
    response_length: Optional[int] = None
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None
    title: Optional[str] = None
    model: Optional[str] = None
    chat_core_version: str = "0.0.1"
    app_version: str = "1.9.0"
