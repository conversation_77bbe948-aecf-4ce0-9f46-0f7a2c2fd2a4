from typing import <PERSON><PERSON>

from dotenv import load_dotenv
from pydantic import BaseModel
from pydantic_ai import Agent
from pydantic_ai.agent import AgentRunResult

from ai_models import get_ai_provider_factory


class SummaryAgent(BaseModel):
    summary: str


class SummaryAndTopicAgent(BaseModel):
    summary: str
    topic: str


async def extract_summary_by_agent(query: str, answer: str, include_title=False) -> Tuple[str, str]:
    summary, topic = "", ""
    result_type = SummaryAgent
    system_prompt = f"""
        <conversation>
        QUESTION: {query}
        ANSWER: {answer}
        <conversation\>
        Create precise, searchable summary of the above legal conversation queation and answer, optimized for Hebrew semantic search systems.
        ## Output Requirements:
        1. Language: Hebrew
        2. Length: 300 characters
        3. Content:
                  synthesis of the conversation including:
                  - Core discussion topics
                  - Key terms and concepts
                  - Essential context markers
                  - Relevant technical/legal terminology
                  - Primary themes for retrieval
        4. Format: <summary>summary only with no prefix headlins</summary>
        """

    if include_title:
        system_prompt += """
        5. Include a topic for the chat up to 7 words, that captures the essence of the conversation.
        """
        result_type = SummaryAndTopicAgent

    agent = Agent(
        model='anthropic:claude-3-5-haiku-latest',
        deps_type=str,
        system_prompt=system_prompt,
        result_type=result_type,
    )

    dice_result = await agent.run(query)

    if isinstance(dice_result, AgentRunResult):
        # Extract the summary from the result
        summary = dice_result.data.summary
        if include_title:
            topic = dice_result.data.topic
    else:
        print("Error: The result is not of type AgentRunResult.")
    return summary, topic


async def main():
    ai_provider = get_ai_provider_factory()
    query = """
    באילו מקרים עורך דין רשאי להעיד מטעם לקוחות?
    """
    answer = """
    nעל פי החומר המשפטי שסופק, אפרט את המקרים בהם עורך דין רשאי להעיד מטעם לקוחו:\n\n## הכלל והחריגים\n\nהכלל הבסיסי קובע כי עורך דין לא ייצג אדם אם הוא עומד להעיד מטעמו. אם נקרא עורך הדין להעיד מטעמו של אדם לאחר שהחל לייצגו - יפסיק את הייצוג.\n\nהחריגים המאפשרים לעורך דין להעיד מטעם לקוחו הם:\n\n1. כאשר הוא נקרא להעיד על-ידי בעל דין יריב\n2. כאשר העדות היא בעניין טכני\n3. כאשר ניתנה רשות בית המשפט\n4. כאשר ניתנה רשות מאת הועד המחוזי\n\n## הרציונל מאחורי החריגים\n\nהחריגים נועדו לאפשר לעורך הדין להעיד במקרים שבהם:\n- לא קיים חשש לניגוד עניינים \n- אין חשש לחוסר אובייקטיביות\n- יש צורך למנוע שימוש לרעה באיסור על-ידי הצד שכנגד\n\nלדוגמה, ללא החריג המאפשר עדות כשהצד שכנגד מזמין את עורך הדין להעיד, היה יכול נתבע המעוניין שהתובע לא ייוצג על-ידי עורך דין מצליח, לזמן את עורך דינו של התובע לעדות ובכך לחייבו להפסיק את הייצוג.\n\n## דוגמה מהפסיקה \n\nבפרשת מרום נדונה השאלה האם יש פסול בכך שבאת כוחה של מרום, אשר ערכה את הצוואה, העידה במשפט על נסיבות עריכתה. נפסק כי על פי כלל 36 לכללי האתיקה, גם אם העדות אינה פסולה - יש בכך כדי להפחית ממשקלה.\n\nהנושא מופיע בספרי מחשבות בפרקים הבאים:\n- ספר \"אתיקה מקצועית - מיקודית\", פרק ו': עורך הדין ועדים\n- ספר \"אתיקה מקצועית - סיכומי פסיקה\", פרק ו': עורך הדין ועדים
    """
    r, t = await extract_summary_by_agent(query=query, answer=answer, include_title=True)
    print(f"Summary: {r}")
    print(f"Topic: {t}")


if __name__ == '__main__':
    load_dotenv()

    # agent_ner()
    import asyncio

    asyncio.run(main())
