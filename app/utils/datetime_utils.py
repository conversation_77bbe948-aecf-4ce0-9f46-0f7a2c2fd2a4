from datetime import datetime
from typing import Union

from pydantic import field_validator


def timestamp_now_il():
    """
    Get the current timestamp based on the Asia/Jerusalem timezone.
    """
    return datetime.now(pytz.timezone("Asia/Jerusalem"))


def timestamp_now_utc() -> datetime:
    """
    Get the current UTC timestamp.
    """
    return datetime.now(pytz.utc)


def timestamp_utc_to_jerusalem(timestamp: datetime,string_foramt=False) -> Union[datetime, str]:
    """
    Convert UTC timestamp to JST (Japan Standard Time).

    :param timestamp: UTC datetime object.
    :param string_foramt: If True, return the timestamp as a string in JST format.
    :return: JST datetime object.
    """
    if timestamp.tzinfo is None:
        timestamp = pytz.utc.localize(timestamp)
    if string_foramt:
        return timestamp.astimezone(pytz.timezone("Asia/Jerusalem")).strftime("%H:%M %d/%m/%Y")


    return timestamp.astimezone(pytz.timezone("Asia/Jerusalem"))


from datetime import datetime
import pytz

def normalize_to_utc(dt: datetime) -> datetime:
    if dt.tzinfo is None:
        dt = pytz.timezone("Asia/Jerusalem").localize(dt)
    return dt.astimezone(pytz.utc)


def convert_datetime_to_iso(value):
    """
    Converts a datetime object to an ISO format string.
    Returns the original value if it's not a datetime object or is None.
    """
    if isinstance(value, datetime):
        return value.isoformat()
    return value


def utc_datetime_field_validator(*fields):
    @field_validator(*fields, mode="before")
    def _validator(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            v = datetime.fromisoformat(v)
        return normalize_to_utc(v)
    return _validator