from datetime import datetime
from enum import Enum
from typing import Optional, List

from pydantic import BaseModel, Field

from utils.datetime_utils import timestamp_now_utc, timestamp_now_il


class RoleEnum(str, Enum):
    assistant = "assistant"
    user = "user"


class ConversationEnum(Enum):
    default_val = -1
    PRINTABLE = 0
    MODEL_CONVERSATION = 1
    BOTH = 2

    @classmethod
    def from_value(cls, value):
        """Return the Enum member name from its value."""
        for member in cls:
            if member.value == value:
                return member
        raise ValueError(f"No matching Enum for value: {value}")


import uuid


class CitationIndex(BaseModel):
    """Represents the start and end indices of a citation in a message"""
    start: int = 1
    end: int = 1


class ConversationItem(BaseModel):
    """Represents a single message in a conversation"""
    conversation_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    role: RoleEnum
    content: str
    timestamp: datetime = Field(default_factory=timestamp_now_il)
    action: ConversationEnum
    prompt: Optional[str] = None
    citations_index: Optional[CitationIndex] = Field(default_factory=CitationIndex)
    entry_type: Optional[str] = None
    index: int = 0


class ConversationManager:
    def __init__(self, conversation: Optional[List[ConversationItem]] = None, session_index: int = 1):
        self.conversation = conversation or []
        self.session_index = session_index
        self.multiple_answer = False

    def replace_last_query_if_needed(self, item: ConversationItem) -> bool:
        """Replace last query if needed if last item is a query."""
        last_item = self.conversation[-1]
        if last_item.entry_type == "query" and item.entry_type == "query":
            item.index = last_item.index
            self.conversation[-1] = item
            return True
        return False

    def replace_last_answer_if_needed(self, item: ConversationItem) -> bool:
        """Replace last answer if needed if last item is an answer."""

        last_item = self.conversation[-1]
        if last_item.entry_type == "answer" and item.entry_type == "answer" and not self.multiple_answer:
            item.index = last_item.index
            self.conversation[-1] = item
            return True
        return False

    def add(self, item: ConversationItem, multiple_answer: bool = False):
        """Add a ConversationItem, managing indexes correctly."""
        self.multiple_answer = multiple_answer
        if item.entry_type not in ("query", "answer", "None"):
            raise ValueError(f"Only 'query' or 'answer' are allowed. Got '{item.entry_type}'.")

        if not self.conversation:
            item.index = 1
            self.session_index = 1
            self.conversation.append(item)
            return

        ##If the last item is a query and the current item is also a query, replace the last item with the current one.
        if self.replace_last_query_if_needed(item) or self.replace_last_answer_if_needed(item):
            return

        last_item = self.conversation[-1]

        if last_item.entry_type == "query" and item.entry_type == "answer":
            # same index
            item.index = last_item.index
            self.conversation.append(item)

        elif last_item.entry_type == "answer" and item.entry_type == "query":
            # increment index
            self.session_index += 1
            item.index = self.session_index
            self.conversation.append(item)
        elif item.entry_type == "None" or (last_item.entry_type == "None" and item.entry_type == "answer"):
            item.index = self.session_index
            self.conversation.append(item)
        elif last_item.entry_type == "answer" and item.entry_type == "answer" and (
                self.multiple_answer and last_item.role != item.role):
            # only for the trainer chat, if the last item is an answer and the current item is also an answer, but the roles are different, we append it with the same index
            item.index = self.session_index
            self.conversation.append(item)
