from typing import Optional, List, Dict

from pydantic import BaseModel, Field

from utils.pre_chat_dto import Filters, ModelSettings, ChatSettings, DataContext
from utils.conversation_manager import ConversationItem


class RedisSchema(BaseModel):
    """
    Schema for chat data stored in Redis.
    This is a unified schema for all chat types.
    """
    user_id: str
    chat_id: str
    domain: str
    query: Optional[str] = None
    answer: Optional[str] = None
    conversation: List[ConversationItem] = Field(default_factory=list)
    summaries: Optional[List[str]] = []
    citations: Optional[List[Dict]] = Field(default_factory=list)
    data: Optional[DataContext] = Field(default_factory=DataContext)
    model_settings: ModelSettings = Field(default_factory=ModelSettings)
    filters: Optional[Filters] = Field(default_factory=dict)
    chat_settings: ChatSettings = Field(default_factory=ChatSettings)
