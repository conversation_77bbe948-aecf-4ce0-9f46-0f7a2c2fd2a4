"""
Dependency injection for encryption services
"""
from fastapi import Depends
from motor.motor_asyncio import AsyncIOMotorClient

from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.user_key_manager import UserKeyManager
from chat_history.services.encrypt_data_manager import ChatHistoryManager
from api.dependencies.mongo_db import get_mongo_client
import configs.app_config as conf


def get_encryption_manager() -> EncryptionManager:
    """
    FastAPI dependency for EncryptionManager singleton

    Returns:
        EncryptionManager: The singleton encryption manager instance

    Raises:
        RuntimeError: If EncryptionManager is not initialized
    """
    try:
        return EncryptionManager.get_instance()
    except RuntimeError as e:
        raise RuntimeError("EncryptionManager not initialized. Ensure init_encryption_services() is called on startup.") from e





def get_user_key_manager(
    encryption_manager: EncryptionManager = Depends(get_encryption_manager)
) -> UserKeyManager:
    """
    FastAPI dependency for UserKeyManager

    Args:
        encryption_manager: Injected EncryptionManager dependency

    Returns:
        UserKeyManager: New instance with proper dependencies
    """
    # Create UserKeyManager with injected EncryptionManager (master key only)
    mongo_uri = conf.MONGO_URL.format(conf.MONGO_USER_NAME, conf.MONGO_PASSWORD)
    return UserKeyManager(
        encryption_manager=encryption_manager,
        mongo_uri=mongo_uri,
        db_name=conf.MONGO_DB_HISTORY,
        collection_name=conf.MONGO_USERS_COLLECTION
    )


def get_chat_history_manager(
    encryption_manager: EncryptionManager = Depends(get_encryption_manager),
    mongo_client: AsyncIOMotorClient = Depends(get_mongo_client),
    user_key_manager: UserKeyManager = Depends(get_user_key_manager)
) -> ChatHistoryManager:
    """
    FastAPI dependency for ChatHistoryManager with proper dependency injection

    Args:
        encryption_manager: Injected EncryptionManager dependency
        mongo_client: Injected MongoDB client dependency
        user_key_manager: Injected UserKeyManager dependency

    Returns:
        ChatHistoryManager: New instance with all required dependencies injected
    """
    return ChatHistoryManager(
        encryption_manager=encryption_manager,
        mongo_client=mongo_client,
        user_key_manager=user_key_manager
    )
