import logging
import os
from enum import Enum
import json

class LogFormatEnum(str, Enum):
    excel = "excel"
    dictionary = "dictionary"

LOG_FORMAT = LogFormatEnum.dictionary

def get_log_level(log_level_str: str) -> int:    
    return {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }.get(log_level_str.upper())  # can raise a KeyError

DEFAULT_LOG_LEVEL = get_log_level(os.getenv("LOG_LEVEL", 'INFO'))

def escape_newlines(record):
    record.msg = record.getMessage().replace('\n', '\\n')
    return True


# TODO Is this function relevant only to Takdin-AI or can be used by other repos? - Shall we move it to general infrastructure repo so all repos will use it?
def get_logger(name, level=DEFAULT_LOG_LEVEL):
    # Set up the logger
    logger = logging.getLogger(name)

    # Check if the logger already has handlers
    if logger.handlers:
        return logger  # Return early if handlers already set

    logger.setLevel(level)

    # Log file path
    # TODO This logger writes in the file system, we currently using Coralogix. Can we inherit the logger and do implementation with Coralogix?
    # os.makedirs('logs', exist_ok=True)
    # log_file_path = os.path.join("logs", "app.log")

    # Create a rotating file handler to log messages to a file
    max_log_size = 10 * 1024 * 1024  # 10MB
    backup_count = 3  # Keep 3 backup copies
    # file_handler = RotatingFileHandler(log_file_path, maxBytes=max_log_size, backupCount=backup_count, encoding='utf-8')
    # file_handler.setLevel(level)

    # Create a stream handler to stream log messages to stdout
    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(level)
    stream_handler.addFilter(escape_newlines)

    # Set the encoding of the stream handler to 'utf-8'
    stream_handler.encoding = 'utf-8'

    # Set up the formatter for the log messages
    log_format = "%(asctime)s | PID:%(process)d | TID:%(thread)xd | %(filename)s:%(lineno)d | %(name)s | %(levelname)s | %(message)s" if LOG_FORMAT == LogFormatEnum.excel else json.dumps({'asctime':'%(asctime)s','process':'PID:%(process)d','thread':'TID:%(thread)xd','filename_and_lineno':'%(filename)s:%(lineno)d','name':'%(name)s','levelname':'%(levelname)s', 'message':'%(message)s'})

    formatter = logging.Formatter(log_format)

    # file_handler.setFormatter(formatter)
    stream_handler.setFormatter(formatter)

    # Add the handlers to the logger
    # logger.addHandler(file_handler)
    logger.addHandler(stream_handler)

    return logger

app_logger = get_logger('app')


def log_format(log_dic):
    return '|'.join(str(value) for value in log_dic.values()) if LOG_FORMAT == LogFormatEnum.excel else json.dumps(log_dic)


