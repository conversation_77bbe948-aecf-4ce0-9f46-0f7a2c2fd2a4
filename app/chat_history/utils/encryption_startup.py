"""
Encryption startup utility - Simple function to initialize encryption services
Can be used in any main.py file across different Dockerfiles
"""
from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.master_key_service import MasterKeyService
from middlewares.logging_utils import app_logger as logger


async def init_encryption_manager():
    """
    Initialize EncryptionManager with KMS master key
    
    Simple function that can be called from any main.py file
    
    Raises:
        Exception: If initialization fails
    """
    try:
        logger.info("Initializing EncryptionManager...")
        
        # Get master key from environment
        master_key = await MasterKeyService.get_master_key()
        
        # Initialize EncryptionManager
        EncryptionManager.initialize(master_key)
        
        logger.info("EncryptionManager initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize EncryptionManager: {e}")
        raise


def is_encryption_manager_ready() -> bool:
    """
    Check if EncryptionManager is ready
    
    Returns:
        bool: True if EncryptionManager is initialized
    """
    try:
        EncryptionManager.get_instance()
        return True
    except RuntimeError:
        return False
