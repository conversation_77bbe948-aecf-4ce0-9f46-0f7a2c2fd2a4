from typing import Dict, Any, Optional
from pydantic import BaseModel

from middlewares.logging_utils import app_logger as logger


class SchemaMigration:
    """Handle schema migrations for MongoDB documents"""

    CURRENT_VERSION = "1.0"

    @staticmethod
    def migrate_document(document: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate a document to the current schema version
        
        Args:
            document: Raw document from MongoDB
            
        Returns:
            Migrated document compatible with current schema
        """
        if not document:
            return document

        # Get current version from document, default to "0.9" for legacy documents
        current_version = document.get("schema_version", "1.0")

        if current_version == SchemaMigration.CURRENT_VERSION:
            return document

        logger.info(f"Migrating document from version {current_version} to {SchemaMigration.CURRENT_VERSION}")

        # Apply migrations in sequence
        migrated_document = document.copy()

        if current_version == "0.9":
            migrated_document = SchemaMigration._migrate_from_0_9_to_1_0(migrated_document)
            current_version = "1.0"

        # Add future migrations here
        # if current_version == "1.0":
        #     migrated_document = SchemaMigration._migrate_from_1_0_to_1_1(migrated_document)
        #     current_version = "1.1"

        return migrated_document

    @staticmethod
    def _migrate_from_0_9_to_1_0(document: Dict[str, Any]) -> Dict[str, Any]:
        """Migrate from version 0.9 to 1.0"""
        migrated = document.copy()

        # Add schema version
        migrated["schema_version"] = "1.0"

        # Add premium user fields if not present

        # Ensure encrypted fields are properly initialized
        if "encrypted_conversation" not in migrated:
            migrated["encrypted_conversation"] = None

        if "conversation_hash" not in migrated:
            migrated["conversation_hash"] = None

        logger.info(f"Successfully migrated document {migrated.get('chat_id', 'unknown')} from 0.9 to 1.0")
        return migrated



    @staticmethod
    def is_compatible_version(version: str) -> bool:
        """
        Check if a schema version is compatible with current implementation
        
        Args:
            version: Schema version to check
            
        Returns:
            True if compatible, False otherwise
        """
        compatible_versions = ["0.9", "1.0"]
        return version in compatible_versions

    @staticmethod
    def get_migration_info(from_version: str, to_version: str) -> Optional[str]:
        """
        Get information about what changes in a migration
        
        Args:
            from_version: Source version
            to_version: Target version
            
        Returns:
            Description of migration changes
        """
        migrations = {
            ("0.9", "1.0"): "Added schema versioning, premium user fields, and encryption field initialization"
        }

        return migrations.get((from_version, to_version))


