import json
import hashlib
from typing import List

from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from fastapi.encoders import jsonable_encoder

from utils.conversation_manager import ConversationItem


class EncryptionManager:
    _instance = None
    _kms_master_key = None

    @classmethod
    def initialize(cls, kms_master_key: bytes):
        """Initialize the class with KMS master key"""
        if cls._instance is None:
            cls._kms_master_key = kms_master_key
            cls._instance = cls()
        return cls._instance

    @classmethod
    def get_instance(cls):
        """Return the stored instance"""
        if cls._instance is None:
            raise RuntimeError("EncryptionManager has not been initialized. Call initialize() first.")
        return cls._instance

    def encrypt(self, data: bytes) -> bytes:
        nonce = Fernet.generate_key()[:12]

        cipher = Cipher(algorithms.AES(self._kms_master_key), modes.GCM(nonce))
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(data) + encryptor.finalize()
        return nonce + encryptor.tag + ciphertext

    def decrypt(self, encrypted_data: bytes) -> bytes:
        nonce = encrypted_data[:12]
        tag = encrypted_data[12:28]
        ciphertext = encrypted_data[28:]

        cipher = Cipher(algorithms.AES(self._kms_master_key), modes.GCM(nonce, tag))
        decryptor = cipher.decryptor()
        return decryptor.update(ciphertext) + decryptor.finalize()

    @staticmethod
    def generate_user_key() -> bytes:
        # Generate 32-byte key for AES-256
        fernet_key = Fernet.generate_key()
        # Use first 32 bytes for AES
        return fernet_key[:32]

    def encrypt_user_key(self, user_key: bytes) -> bytes:
        """
        Encrypt a user key using the master key

        Args:
            user_key: User's 32-byte encryption key

        Returns:
            bytes: Encrypted user key
        """
        return self.encrypt(user_key)

    def decrypt_user_key(self, encrypted_user_key: bytes) -> bytes:
        """
        Decrypt a user key using the master key

        Args:
            encrypted_user_key: Encrypted user key bytes

        Returns:
            bytes: Decrypted 32-byte user key
        """
        return self.decrypt(encrypted_user_key)

    def encrypt_conversation(self, conversation: List[ConversationItem]) -> bytes:
        """
        Encrypt a list of ConversationItem objects
        
        Args:
            conversation: List of ConversationItem objects
            
        Returns:
            bytes: Encrypted conversation data
        """
        # Convert conversation to JSON string
        conversation_str = json.dumps(conversation, default=jsonable_encoder)
        conversation_bytes = conversation_str.encode('utf-8')

        # Encrypt the data
        return self.encrypt(conversation_bytes)

    def decrypt_conversation(self, encrypted_data: bytes) -> List[ConversationItem]:
        """
        Decrypt conversation data back to List[ConversationItem]
        
        Args:
            encrypted_data: Encrypted conversation bytes
            
        Returns:
            List[ConversationItem]: Decrypted conversation items
        """
        # Decrypt the data
        decrypted_bytes = self.decrypt(encrypted_data)
        conversation_json = decrypted_bytes.decode('utf-8')
        conversation_data = json.loads(conversation_json)

        # Convert back to ConversationItem objects
        return [ConversationItem(**item) for item in conversation_data]

    def generate_conversation_hash(self, conversation: List[ConversationItem]) -> str:
        """
        Generate SHA-256 hash of conversation for integrity verification
        
        Args:
            conversation: List of ConversationItem objects
            
        Returns:
            str: SHA-256 hash of the conversation
        """
        # Convert conversation to consistent JSON representation
        # conversation_data = [item.model_dump() for item in conversation]
        # conversation_data.sort(key=lambda x: (x.get('timestamp', ''), x.get('index', 0)))
        conversation_str = json.dumps(conversation, default=jsonable_encoder)

        conversation_bytes = conversation_str.encode('utf-8')

        # Generate SHA-256 hash
        return hashlib.sha256(conversation_bytes).hexdigest()

    def verify_conversation_hash(self, conversation: List[ConversationItem], expected_hash: str) -> bool:
        """
        Verify conversation integrity using hash
        
        Args:
            conversation: List of ConversationItem objects
            expected_hash: Expected hash value
            
        Returns:
            bool: True if hash matches, False otherwise
        """
        calculated_hash = self.generate_conversation_hash(conversation)
        return calculated_hash == expected_hash

    def encrypt_conversation_with_user_key(self, conversation: List[ConversationItem], user_key: bytes) -> bytes:
        """
        Encrypt a list of ConversationItem objects using a specific user key
        
        Args:
            conversation: List of ConversationItem objects
            user_key: User's encryption key
            
        Returns:
            bytes: Encrypted conversation data
        """
        # Convert conversation to JSON string
        conversation_str = json.dumps(conversation, default=jsonable_encoder)

        conversation_bytes = conversation_str.encode('utf-8')

        # Create user-specific cipher
        nonce = Fernet.generate_key()[:12]
        cipher = Cipher(algorithms.AES(user_key), modes.GCM(nonce))
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(conversation_bytes) + encryptor.finalize()

        return nonce + encryptor.tag + ciphertext

    def decrypt_conversation_with_user_key(self, encrypted_data: bytes, user_key: bytes) -> List[ConversationItem]:
        """
        Decrypt conversation data back to List[ConversationItem] using a specific user key
        
        Args:
            encrypted_data: Encrypted conversation bytes
            user_key: User's encryption key
            
        Returns:
            List[ConversationItem]: Decrypted conversation items
        """
        # Extract components
        nonce = encrypted_data[:12]
        tag = encrypted_data[12:28]
        ciphertext = encrypted_data[28:]

        # Create user-specific cipher
        cipher = Cipher(algorithms.AES(user_key), modes.GCM(nonce, tag))
        decryptor = cipher.decryptor()
        decrypted_bytes = decryptor.update(ciphertext) + decryptor.finalize()

        # Convert back to conversation items
        conversation_json = decrypted_bytes.decode('utf-8')
        conversation_data = json.loads(conversation_json)

        return [ConversationItem(**item) for item in conversation_data]


if __name__ == '__main__':
    # Example usage
    kms_master_key = EncryptionManager.generate_user_key()
    encryption_manager = EncryptionManager.initialize(kms_master_key)

    conversation = [
        ConversationItem(index=0, role='user', content='Hello'),
        ConversationItem(index=1, role='assistant', content='Hi there!')
    ]

    encrypted_data = encryption_manager.encrypt_conversation(conversation)
    decrypted_conversation = encryption_manager.decrypt_conversation(encrypted_data)

    assert conversation == decrypted_conversation, "Decrypted conversation does not match original"

    print("Encryption and decryption successful!")
