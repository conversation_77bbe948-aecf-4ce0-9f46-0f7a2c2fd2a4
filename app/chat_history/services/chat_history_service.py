import json
import traceback

from pymongo.errors import PyMongoError

from chat_history.utils.history_utils import adjust_chat_history, OrderByEnum, get_mongo_field
from chat_history.schema.mongo_models import MongoChatSchema
from chat_history.schema.schema_migration import SchemaMigration
from utils.datetime_utils import timestamp_now_il

from api.dependencies.mongo_db import get_mongo_client, get_collection_with_tz
from configs.app_config import MONGO_DB_HISTORY, MONGO_CHAT_HISTORY_COLLECTION
from middlewares.logging_utils import app_logger as logger
from pydantic.json import pydantic_encoder

from typing import Optional, Dict, Any, List
from motor.motor_asyncio import AsyncIOMotorClient


async def _decrypt_conversation_if_needed(
    chat_document: Dict[str, Any],
    chat_history_manager,
    user_id: str,
    chat_id: str
) -> Dict[str, Any]:
    """
    Helper function to decrypt conversation if encrypted data exists and decryption is possible.
    Supports transition period with both encrypted and unencrypted formats.

    Args:
        chat_document: Raw chat document from MongoDB
        chat_history_manager: ChatHistoryManager instance for decryption
        user_id: User ID for validation and decryption
        chat_id: Chat ID for logging

    Returns:
        Chat document with decrypted conversation if successful, otherwise original document
    """
    if not chat_history_manager or not user_id:
        logger.debug(f"No decryption manager or user_id provided for chat {chat_id}")
        return chat_document

    # Check if user owns this chat
    if chat_document.get('user_id') != user_id:
        logger.warning(f"User {user_id} does not own chat {chat_id}")
        return chat_document

    # Check if encrypted conversation exists
    encrypted_conversation = chat_document.get('encrypted_conversation')
    if not encrypted_conversation:
        logger.debug(f"No encrypted conversation found for chat {chat_id}, using plain conversation")
        return chat_document

    try:
        # Use ChatHistoryManager to decrypt the conversation
        logger.debug(f"Attempting to decrypt conversation for chat {chat_id}")
        decrypted_chat = await chat_history_manager.extract_chat_history(chat_id, user_id)

        if decrypted_chat and hasattr(decrypted_chat, 'conversation'):
            # Convert back to dict format for API response
            chat_document['conversation'] = [
                item.model_dump() if hasattr(item, 'model_dump') else item
                for item in decrypted_chat.conversation
            ]
            logger.info(f"Successfully decrypted conversation for chat {chat_id}")

            # Remove encrypted data from response for security
            if 'encrypted_conversation' in chat_document:
                del chat_document['encrypted_conversation']
            if 'conversation_hash' in chat_document:
                del chat_document['conversation_hash']
        else:
            logger.warning(f"Decryption returned empty result for chat {chat_id}")

    except Exception as e:
        logger.error(f"Failed to decrypt conversation for chat {chat_id}: {e}", exc_info=True)
        # Continue with original data - don't fail the request
        # During transition period, fallback to unencrypted conversation if available
        if not chat_document.get('conversation'):
            logger.warning(f"No fallback conversation available for chat {chat_id}")

    return chat_document


def _apply_schema_migration(chat_document: Dict[str, Any]) -> Dict[str, Any]:
    """
    Apply schema migration to ensure document compatibility.

    Args:
        chat_document: Raw chat document from MongoDB

    Returns:
        Migrated chat document
    """
    try:
        return SchemaMigration.migrate_document(chat_document)
    except Exception as e:
        logger.warning(f"Schema migration failed: {e}")
        return chat_document


async def get_user_chats(
        mongo_client: AsyncIOMotorClient,
        user_id: str,
        offset: int = 0,
        limit: int = 20,
        order_by: OrderByEnum = OrderByEnum.UPDATED
) -> Dict[str, Any]:
    """
    Get user chats with pagination.

    Handles both encrypted and unencrypted chat formats during transition period.
    Only returns chat metadata (no conversation content) for performance.

    Args:
        mongo_client: MongoDB client
        user_id: User ID to filter chats
        offset: Number of records to skip
        limit: Maximum number of records to return
        order_by: Sort order (updated or created)

    Returns:
        Paginated chat list with metadata

    Raises:
        ConnectionError: If database error occurs
    """
    try:
        history_chat_collection = await get_collection_with_tz(
            mongo_client,
            db_name=MONGO_DB_HISTORY,
            collection_name=MONGO_CHAT_HISTORY_COLLECTION
        )

        query = {"user_id": user_id, "chat_settings.status": "active"}
        mongo_field = get_mongo_field(order_by)

        total_count = await history_chat_collection.count_documents(query)

        # Only fetch metadata fields for performance
        # Don't fetch conversation or encrypted_conversation for list view
        cursor = history_chat_collection.find(
            query,
            {
                "chat_id": 1,
                "filters": 1,
                "chat_settings": 1,
            }
        ).sort(mongo_field, -1).skip(offset).limit(limit)

        documents = await cursor.to_list(length=limit)

        chats = []
        for doc in documents:
            try:
                chat_settings = doc.get('chat_settings', {})
                filters = doc.get('filters', {})

                if chat_settings.get("status") == "active":
                    chats.append({
                        "chat_id": doc.get('chat_id'),
                        "filters": filters,
                        "chat_settings": json.loads(
                            json.dumps(chat_settings, default=pydantic_encoder)
                        )
                    })
            except Exception as e:
                logger.warning(f"Error processing chat {doc.get('chat_id', 'unknown')}: {e}")
                # Continue with other chats

        return {
            "chats": chats,
            "total_count": total_count,
            "offset": offset,
            "limit": limit,
            "count": len(chats),
            "has_more": (offset + len(documents)) < total_count,
            "order_by": order_by.value
        }

    except PyMongoError as e:
        logger.error(f"MongoDB error in get_user_chats: {e}", exc_info=True)
        raise ConnectionError(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Error in get_user_chats: {e}", exc_info=True)
        raise


async def get_chat_by_id(
        mongo_client: AsyncIOMotorClient,
        chat_id: str,
        user_id: Optional[str] = None,
        chat_history_manager=None
) -> Optional[Dict[str, Any]]:
    """
    Get chat by chat_id with automatic encryption/decryption support.

    Supports transition period with both encrypted and unencrypted conversation formats.
    Automatically decrypts conversations when possible and falls back gracefully.

    Args:
        mongo_client: MongoDB client
        chat_id: Chat ID to retrieve
        user_id: User ID for validation and decryption
        chat_history_manager: ChatHistoryManager for decryption operations

    Returns:
        Chat document with decrypted conversation if available

    Raises:
        ValueError: If chat not found or not active
        ConnectionError: If database error occurs
    """
    try:
        history_chat_collection = await get_collection_with_tz(
            mongo_client,
            db_name=MONGO_DB_HISTORY,
            collection_name=MONGO_CHAT_HISTORY_COLLECTION
        )

        chat_document = await history_chat_collection.find_one({"chat_id": chat_id})

        if not chat_document:
            logger.warning(f"Chat not found: chat_id={chat_id}")
            raise ValueError(f"Chat not found: {chat_id}")

        # Apply schema migration for backward compatibility
        chat_document = _apply_schema_migration(chat_document)

        if chat_document.get('chat_settings', {}).get('status') != 'active':
            logger.warning(f"Chat is not active: chat_id={chat_id}")
            raise ValueError(f"Chat is not active: {chat_id}")

        # Attempt to decrypt conversation if encryption manager is available
        if chat_history_manager and user_id:
            chat_document = await _decrypt_conversation_if_needed(
                chat_document, chat_history_manager, user_id, chat_id
            )

        # Apply chat history adjustments
        chat_document = adjust_chat_history(dict(chat_document))
        return chat_document

    except ValueError:
        # Re-raise ValueError (chat not found, not active)
        raise
    except PyMongoError as e:
        logger.error(f"MongoDB error in get_chat_by_id: {e}", exc_info=True)
        raise ConnectionError(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Error in get_chat_by_id: {e}", exc_info=True)
        raise


async def update_chat_title(
        mongo_client: AsyncIOMotorClient,
        chat_id: str,
        new_title: str,
        user_id: Optional[str] = None
) -> bool:
    """
    Update chat title.

    Works with both encrypted and unencrypted chat formats.
    Only updates metadata, does not affect conversation encryption.

    Args:
        mongo_client: MongoDB client
        chat_id: Chat ID to update
        new_title: New title for the chat
        user_id: User ID for validation (optional but recommended)

    Returns:
        True if update successful, False if chat not found

    Raises:
        ConnectionError: If database error occurs
    """
    try:
        history_chat_collection = await get_collection_with_tz(
            mongo_client,
            db_name=MONGO_DB_HISTORY,
            collection_name=MONGO_CHAT_HISTORY_COLLECTION
        )

        # Build query with user validation for security
        query = {"chat_id": chat_id, "chat_settings.status": "active"}
        if user_id:
            query["user_id"] = user_id

        update_time = timestamp_now_il()

        result = await history_chat_collection.update_one(
            query,
            {
                "$set": {
                    "chat_settings.title": new_title,
                    "chat_settings.update_time": update_time
                }
            }
        )

        if result.modified_count > 0:
            logger.info(f"Chat title updated: chat_id={chat_id}, new_title='{new_title}'")
            return True
        else:
            logger.warning(f"No chat found to update: chat_id={chat_id}, user_id={user_id}")
            return False

    except PyMongoError as e:
        logger.error(f"MongoDB error in update_chat_title: {e}", exc_info=True)
        raise ConnectionError(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Error in update_chat_title: {e}", exc_info=True)
        raise


async def delete_chat_by_id(mongo_client: AsyncIOMotorClient, chat_id: str, user_id: str) -> bool:
    """
    Delete chat by chat_id.

    Safely deletes both encrypted and unencrypted chats.
    Ensures user owns the chat before deletion.

    Args:
        mongo_client: MongoDB client
        chat_id: Chat ID to delete
        user_id: User ID for validation

    Returns:
        True if deletion successful, False if chat not found

    Raises:
        ConnectionError: If database error occurs
    """
    try:
        history_chat_collection = await get_collection_with_tz(
            mongo_client,
            db_name=MONGO_DB_HISTORY,
            collection_name=MONGO_CHAT_HISTORY_COLLECTION
        )

        # Delete with user validation for security
        result = await history_chat_collection.delete_one({
            "chat_id": chat_id,
            "user_id": user_id,
            "chat_settings.status": "active"
        })

        if result.deleted_count > 0:
            logger.info(f"Chat deleted: chat_id={chat_id}, user_id={user_id}")
            return True
        else:
            logger.warning(f"No chat found to delete: chat_id={chat_id}, user_id={user_id}")
            return False

    except PyMongoError as e:
        logger.error(f"MongoDB error in delete_chat_by_id: {e}", exc_info=True)
        raise ConnectionError(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Error in delete_chat_by_id: {e}", exc_info=True)
        raise


async def create_chat_history_manager_for_testing():
    """
    Create ChatHistoryManager for direct testing (outside FastAPI context).

    This function manually initializes all dependencies needed for testing
    the chat history service directly.

    Returns:
        tuple: (mongo_client, chat_history_manager) or (mongo_client, None) if encryption fails
    """
    try:
        # Initialize encryption services
        from chat_history.utils.encryption_startup import init_encryption_manager
        await init_encryption_manager()

        # Get MongoDB client
        mongo_client = await get_mongo_client()

        # Create dependencies manually
        from chat_history.security.encrypt_manger import EncryptionManager
        from chat_history.services.user_key_manager import UserKeyManager
        from chat_history.services.encrypt_data_manager import ChatHistoryManager
        import configs.app_config as conf

        # Get encryption manager
        encryption_manager = EncryptionManager.get_instance()

        # Create UserKeyManager
        mongo_uri = conf.MONGO_URL.format(conf.MONGO_USER_NAME, conf.MONGO_PASSWORD)
        user_key_manager = UserKeyManager(
            encryption_manager=encryption_manager,
            mongo_uri=mongo_uri,
            db_name=conf.MONGO_DB_HISTORY,
            collection_name=conf.MONGO_USERS_COLLECTION
        )

        # Create ChatHistoryManager
        chat_history_manager = ChatHistoryManager(
            encryption_manager=encryption_manager,
            mongo_client=mongo_client,
            user_key_manager=user_key_manager
        )

        return mongo_client, chat_history_manager

    except Exception as e:
        logger.error(f"Failed to create ChatHistoryManager for testing: {e}", exc_info=True)
        # Return MongoDB client without encryption support
        mongo_client = await get_mongo_client()
        return mongo_client, None


async def compare_conversation_formats(
    mongo_client: AsyncIOMotorClient,
    chat_id: str,
    user_id: str,
    chat_history_manager
) -> Dict[str, Any]:
    """
    Compare encrypted and unencrypted conversation formats for testing.

    This function is useful during the transition period to verify that
    encryption/decryption is working correctly.

    Args:
        mongo_client: MongoDB client
        chat_id: Chat ID to compare
        user_id: User ID for validation
        chat_history_manager: ChatHistoryManager for decryption

    Returns:
        Comparison results with both formats and validation status
    """
    try:
        history_chat_collection = await get_collection_with_tz(
            mongo_client,
            db_name=MONGO_DB_HISTORY,
            collection_name=MONGO_CHAT_HISTORY_COLLECTION
        )

        # Get raw document
        chat_document = await history_chat_collection.find_one({"chat_id": chat_id})

        if not chat_document:
            return {"error": f"Chat not found: {chat_id}"}

        if chat_document.get('user_id') != user_id:
            return {"error": f"User {user_id} does not own chat {chat_id}"}

        result = {
            "chat_id": chat_id,
            "user_id": user_id,
            "has_encrypted_conversation": bool(chat_document.get('encrypted_conversation')),
            "has_plain_conversation": bool(chat_document.get('conversation')),
            "conversation_hash": chat_document.get('conversation_hash'),
            "schema_version": chat_document.get('schema_version', 'unknown')
        }

        # Try to decrypt if encrypted conversation exists
        if chat_document.get('encrypted_conversation') and chat_history_manager:
            try:
                decrypted_chat = await chat_history_manager.extract_chat_history(chat_id, user_id)
                if decrypted_chat and hasattr(decrypted_chat, 'conversation'):
                    result["decrypted_conversation_length"] = len(decrypted_chat.conversation)
                    result["decryption_successful"] = True
                else:
                    result["decryption_successful"] = False
                    result["decryption_error"] = "Empty result from decryption"
            except Exception as e:
                result["decryption_successful"] = False
                result["decryption_error"] = str(e)

        # Compare lengths if both formats exist
        plain_conversation = chat_document.get('conversation', [])
        if plain_conversation and result.get("decrypted_conversation_length"):
            result["length_match"] = len(plain_conversation) == result["decrypted_conversation_length"]

        return result

    except Exception as e:
        logger.error(f"Error in compare_conversation_formats: {e}", exc_info=True)
        return {"error": str(e)}


async def main():
    """
    Example usage demonstrating encryption/decryption functionality.

    This main function shows how to use the enhanced chat history service
    with encryption support during the transition period.

    When run directly (not through FastAPI), manually initializes all dependencies.
    """
    try:
        # Initialize environment and dependencies manually (since we're not in FastAPI context)
        print("=== Initializing Dependencies (Direct Execution) ===")

        # Load environment variables
        from dotenv import load_dotenv
        import os
        env_file = os.path.join(os.path.dirname(__file__), '../../..', 'configs', '.env')
        if os.path.exists(env_file):
            load_dotenv(env_file)
            print(f"✓ Loaded environment from: {env_file}")
        else:
            print(f"⚠ Environment file not found: {env_file}")

        # Create dependencies using helper function
        mongo_client, chat_history_manager = await create_chat_history_manager_for_testing()

        if chat_history_manager:
            print("✓ ChatHistoryManager created successfully with encryption support")
        else:
            print("⚠ ChatHistoryManager created without encryption support")

        user_id = "1d349ee1-b6d2-87eb-1bee-3a17b9452968"

        # Example 1: Get user chats (metadata only, no conversation content)
        print("\n=== Getting User Chats ===")
        chats = await get_user_chats(
            mongo_client=mongo_client,
            user_id=user_id,
            offset=0,
            limit=5,
            order_by=OrderByEnum.UPDATED
        )
        print(f"Found {chats['count']} chats out of {chats['total_count']} total")

        # Example 2: Get specific chat with automatic decryption
        if chats['chats']:
            chat_id = chats['chats'][0]['chat_id']
            print(f"\n=== Getting Chat with Decryption: {chat_id} ===")

            chat = await get_chat_by_id(
                mongo_client,
                chat_id,
                user_id=user_id,
                chat_history_manager=chat_history_manager
            )

            if chat:
                conversation_length = len(chat.get('conversation', []))
                print(f"Chat retrieved successfully, conversation length: {conversation_length}")

                # Example 3: Compare conversation formats (for testing)
                if chat_history_manager:
                    print(f"\n=== Comparing Conversation Formats ===")
                    comparison = await compare_conversation_formats(
                        mongo_client, chat_id, user_id, chat_history_manager
                    )
                    print(f"Comparison result: {comparison}")

            # Example 4: Update chat title
            print(f"\n=== Updating Chat Title ===")
            success = await update_chat_title(
                mongo_client=mongo_client,
                chat_id=chat_id,
                new_title=f"Updated Chat - {timestamp_now_il()}",
                user_id=user_id
            )
            print(f"Title update successful: {success}")

        print("\n=== All operations completed successfully ===")

    except Exception as e:
        print(f"Error in main: {e}")
        logger.error(f"Error in main: {e}", exc_info=True)


import asyncio

if __name__ == '__main__':
    # Example usage demonstrating encryption functionality
    asyncio.run(main())
