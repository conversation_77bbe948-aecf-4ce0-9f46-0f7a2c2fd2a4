import logging
import uuid
from collections import defaultdict
from typing import Dict, Any
from typing import List

from numpy.core.defchararray import title
from sqlalchemy import asc, desc, func
from sqlalchemy.exc import NoResultFound, MultipleResultsFound
from sqlalchemy.orm import aliased

import app.models
from api.dependencies.relational_db import get_sql_session
from app import models
from configs import app_config as config
from db_utils.sql_helper import get_text_only_and_full_text
from middlewares.logging_utils import app_logger as logger
from models import AiEntityRelations, AiBookSubChapter, AiBook


class SQLHelper:

    @staticmethod
    def extract_entity_relations(question_id: str, book_id: str):
        """Extract entity relations from the database."""
        with get_sql_session() as db:
            # Fetch the entity relations for the given question_id
            entity_relations = db.query(AiEntityRelations).filter(AiEntityRelations.entityId1 == question_id).all()

            if entity_relations:
                answers = sorted([(er.entityId2, er.entityType2) for er in entity_relations], key=lambda x: x[1],
                                 reverse=True)

                answer_text = db.query(AiBookSubChapter).filter(
                    AiBookSubChapter.subChapterId == answers[0][0]).first().subChapterText
                question_entity = db.query(AiBookSubChapter).filter(
                    AiBookSubChapter.subChapterId == question_id).first()
                book_entity = db.query(AiBook).filter(AiBook.bookId == book_id).first()

                question_text = question_entity.subChapterText if question_entity else None
                try:
                    bookname = book_entity.bookName.split("-")[0] if book_entity else ""

                    question_title = bookname +" "+ question_entity.subChapterTitle if question_entity else ""
                except Exception as e:
                    logger.error(f"Error extracting book name or title: {e}")
                    question_title = ""

                return answer_text, question_text, question_title
            return None, None, None

    @staticmethod
    def import_chunks_by_sub_chapter(sub_chapter_ids: List[str], strict=False) -> defaultdict[Any, list]:
        try:

            table = app.models.AiBookEmbed
            ai_books = aliased(app.models.AiBook)
            with get_sql_session() as sql_session:
                results = (
                    sql_session.query(
                        table.subChapterId,
                        table.cText,
                        table.cId,
                        table.nTokens,
                        table.pageNumber,
                        ai_books.totalPages,
                        ai_books.bookName,
                        ai_books.year,
                        ai_books.legalFieldId,
                        ai_books.bookTypeId,

                    )
                    .join(ai_books, table.bookId == ai_books.bookId)
                    .filter(table.subChapterId.in_(sub_chapter_ids))
                    .all()
                )

            results_dict = defaultdict(list)
            for row in results:
                results_dict[str(row[0])].append({
                    'cText': row[1],
                    'cId': row[2],
                    'nTokens': row[3],
                    'pageNumber': row[4],
                    'subChapterId': row[0],
                    'totalPages': row[5],
                    'bookName': row[6],
                    'year': row[7],
                    'legal_field_id': row[8],
                    'book_type_id': row[9],

                })

            return results_dict
        except Exception as e:
            raise Exception(str(e) if str(e) in config.ERROR_KEYS else "GET_SUB_CHAPTER_TEXT_ERROR")

    @staticmethod
    def import_sub_chapter(sub_chapter_ids):
        try:
            with get_sql_session() as sql_session:
                results = sql_session.query(models.AiBookSubChapter).filter(
                    models.AiBookSubChapter.subChapterId.in_(sub_chapter_ids)).all()
                sql_session.close()
                return {str(row.subChapterId): row for row in results}


        except Exception as e:
            raise Exception(str(e) if str(e) in config.ERROR_KEYS else "GET_SUB_CHAPTER_TEXT_ERROR")

    @staticmethod
    def get_chunk_texts(chunk_ids: List[str], strict=False) -> Dict[str, str]:
        try:
            table = models.AiEmbed
            with get_sql_session() as sql_session:
                results = (sql_session.query(table.cId, table.cText)
                           .filter(table.cId.in_(chunk_ids))
                           .all())

            results_dict = {row[0]: row[1] for row in results}

            # Identify and log missing txt_ids
            missing_chunk_ids = list(set(chunk_ids) - set(results_dict.keys()))
            if missing_chunk_ids:
                msg = f"get_chunk_texts: Missing chunk_ids: {missing_chunk_ids}"
                if strict:
                    raise RuntimeError(msg)
                logger.warning(f'In get_chunk_texts function, Missing chunk ids: {missing_chunk_ids}')
                for key in missing_chunk_ids:
                    if key in results_dict:
                        del results_dict[str(key)]

            return results_dict
        except Exception as e:
            missing = ', '.join(map(str, chunk_ids))
            logger.error(f'Error in get_chunk_texts function with chunk ids {missing}. error - {e}')
            raise Exception(str(e) if str(e) in config.ERROR_KEYS else 'GET_CHUNK_TEXT_ERROR')

    @staticmethod
    def get_html_and_text_only_content(txt_id):

        txt_id = int(txt_id)
        try:
            text_object = get_text_only_and_full_text([txt_id])
            full_text = text_object[txt_id].get('fullText', '')
            text_only = text_object[txt_id].get('textOnly', '')
            if text_only:
                return text_only
            elif full_text:
                return full_text
            else:
                raise Exception('HTML_AND_TEXT_ONLY_CONTENT_ERROR')



        except Exception as e:
            logger.error(f'Error in get_html_and_full_text_content - {e}')
            raise Exception(str(e) if str(e) in config.ERROR_KEYS else 'HTML_AND_TEXT_ONLY_CONTENT_ERROR') from e

    @staticmethod  # verdict
    def get_masters_chunk_row_text(txt_ids: List[int]):
        try:
            with get_sql_session() as sql_session:
                result = sql_session.query(models.AiMasterChunk.txtId, models.AiMasterChunk.decsion,
                                           models.AiMasterChunk.court_ruling
                                           ).filter(
                    models.AiMasterChunk.txtId.in_(txt_ids)).all()
            return {
                row.txtId: {
                    "decsion": row.decsion,
                    "court_ruling": row.court_ruling,
                }
                for row in result
            }

        except Exception as e:
            return {}

    @staticmethod  ###table Metadata return
    def get_maagar_id_and_total_tokens_for_txt_id(txt_id: int) -> app.models.AiMetadata:
        """
        Retrieve the maagarId and total_tokens for a given txt_id from the AiMetadata table.

        Args:
        - sql_session (session): The database session.
        - txt_id (int): The ID for which to retrieve the maagarId and total_tokens.

        Returns:
        - tuple: (maagarId, total_tokens)
        """
        try:
            with get_sql_session() as sql_session:
                metadata_obj = sql_session.query(app.models.AiMetadata).filter_by(txtId=txt_id).one()
                return metadata_obj
        except NoResultFound:
            logger.warning(
                f'In get_maagar_id_and_total_tokens_for_txt_id function, txt_id: {txt_id} not found in AiMetadata table.')
            return None
        except Exception as e:
            logger.error(f'Error in get_maagar_id_and_total_tokens_for_txt_id function. error - {e}')
            raise Exception(str(e) if str(e) in config.ERROR_KEYS else 'GET_MAAGAR_ID_AND_TOTAL_TOKENS_ERROR')

    @staticmethod  ##law
    async def get_law_metadata_by_txt_id(txt_id):
        with get_sql_session() as sql_session:
            query_result = sql_session.query(app.models.AiLawMetadata.txtId, app.models.AiLawMetadata.legislationId,
                                             app.models.AiLawMetadata.title, app.models.AiLawMetadata.provisionText,
                                             app.models.AiLawMetadata.chapterTitle,
                                             app.models.AiLawMetadata.provisionTitle,
                                             app.models.AiLawMetadata.amendmentInformation,
                                             app.models.AiLawMetadata.sectionNumbers,
                                             app.models.AiLawMetadata.cId
                                             ).filter_by(txtId=txt_id).all()

            result_as_dicts = {row.cId:
                {
                    "txt_id": row.txtId,
                    "legislation_id": row.legislationId,
                    "title": row.title,
                    "provision_text": row.provisionText,
                    "chapter_title": row.chapterTitle,
                    "provision_title": row.provisionTitle,
                    "amendment_information": row.amendmentInformation,
                    "section_numbers": row.sectionNumbers,
                    "cid": row.cId
                }
                for row in query_result
            }

        return result_as_dicts

    @staticmethod  ##law and verdict
    async def get_chunks_by_txt_id(txt_id, fetch_all=True):
        with get_sql_session() as sql_session:
            query = sql_session.query(
                app.models.AiEmbed.txtId,
                app.models.AiEmbed.cId,
                app.models.AiEmbed.cText,
                app.models.AiEmbed.nTokens
            ).filter_by(txtId=txt_id)

            if fetch_all:
                rows = query.all()
                return {
                    row.cId: {
                        "txt_id": row.txtId,
                        "cid": row.cId,
                        "cText": row.cText,
                        "nTokens": row.nTokens
                    }
                    for row in rows
                }
            else:
                try:
                    row = query.first()
                    return {
                        "txt_id": row.txtId,
                        "cid": row.cId,
                        "cText": row.cText,
                        "nTokens": row.nTokens
                    }
                except NoResultFound:
                    return None
                except MultipleResultsFound:
                    return {"error": "Multiple chunks found for txt_id"}

    @staticmethod  ##verdict
    def get_three_first_and_last_cids_text(txt_id: int, strict=False) -> Dict[str, Any]:
        try:
            table = models.AiEmbed
            with get_sql_session() as sql_session:
                # ספירת סך הצ'אנקים עבור txt_id הזה
                total_chunks = (
                    sql_session.query(func.count(table.cId))
                    .filter(table.txtId == txt_id)
                    .scalar()
                )

                # שלושת הראשונים לפי סדר עולה
                first_three = (
                    sql_session.query(table.cId, table.cText)
                    .filter(table.txtId == txt_id)
                    .order_by(asc(table.cId))
                    .limit(3)
                    .all()
                )

                # שלושת האחרונים לפי סדר יורד
                last_three = (
                    sql_session.query(table.cId, table.cText)
                    .filter(table.txtId == txt_id)
                    .order_by(desc(table.cId))
                    .limit(3)
                    .all()
                )

            # ממזגים את שתי הרשימות למילון, בלי כפילויות
            combined = {row[0]: row[1] for row in first_three + last_three}

            return {
                "chunks_count": total_chunks,
                "chunks": combined
            }

        except Exception as e:
            logger.error(f'Error in get_three_first_and_last_cids_text function with txt_id {txt_id}. error - {e}')
            raise Exception(str(e) if str(e) in config.ERROR_KEYS else 'FIRST_AND_LAST_CIDS_ERROR')


if __name__ == '__main__':
    import asyncio

    t = asyncio.run(SQLHelper.get_maagar_id_and_total_tokens_for_txt_id(100))
    print(t)
    # import json
    # for i in t:
    #     print(json.dumps(i.__dict__, indent=4, ensure_ascii=False))
