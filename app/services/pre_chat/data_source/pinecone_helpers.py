import asyncio
import re
from time import sleep
from typing import List, Any

import pinecone ##pinecone.Index
from pinecone.core.openapi.db_data.model.scored_vector import ScoredVector
from pinecone_text.hybrid import hybrid_convex_scale
from pinecone.data.index import Index as PineconeInnerIndex


from data_ops.pinecone_func import get_sparse_embedding
from middlewares.logging_utils import app_logger as logger


class PinconeHelper:
    @staticmethod
    def combine_text_and_sparse_embeddings(text_embeddings, sparse_embeddings, search_alpha=0.5):
        try:
            return hybrid_convex_scale(query_dense=text_embeddings, query_sparse=sparse_embeddings, alpha=search_alpha)
        except Exception as e:
            logger.error(f"Error combining text and sparse embeddings: {e}")
            raise e


    @staticmethod
    async def pinecone_search_query(pinecone_index: PineconeInnerIndex , embeddings: List[float], top_k: int, filter_dict: dict,
                                sparse_vector:Any, max_retries: int = 3, delay: int = 2) -> List[ScoredVector]:
        attempts = 0
        while attempts < max_retries:
            try:
                results =  await asyncio.to_thread(pinecone_index.query,
                    vector=embeddings,
                    top_k=top_k,
                    include_metadata=True,
                    filter=filter_dict,
                    sparse_vector=sparse_vector
                )
                matches = results['matches']

                logger.info(f'retry to query in pinecone - attempts: {attempts}') if attempts > 0 else None
                return matches
            except Exception as e:
                attempts += 1
                logger.error(f'Failed in pinecone_query , error: {e} ,filter: {filter_dict} ,attempts: {attempts}')
                if attempts < max_retries:
                    logger.error(f'Retrying in {delay} seconds...')
                    sleep(delay)

        logger.error(f"Error: Max retries exceeded for pinecone query. , filter: {filter_dict}")
        return []


    # @staticmethod
    # def get_sparse_embeddings(domain_name: str, query: str,ner_entities:list) -> list:
    #
    #     is_law=True if domain_name=='law' else False
    #     return get_sparse_embedding(is_law, query)
    #
    #
    @staticmethod
    def get_sparse_embeddings(domain_name: str, query: str, ner_entities: list) -> list:
        is_law = domain_name == 'law'
        if is_law:
            return get_sparse_embedding(is_law, query)

        try:
            entities = [e if isinstance(e, str) else str(e) for e in ner_entities or []]
            entity_set = set(entities)
            combined_entities = []

            query_words = query.split()

            for i in range(len(query_words)):
                word = query_words[i]

                if word in entity_set:
                    combined_entities.append(word)

                    if i + 2 < len(query_words):
                        middle = query_words[i + 1]
                        next_word = query_words[i + 2]

                        if middle not in entity_set and next_word in entity_set:
                            combined_entities.append(middle)

            if len(combined_entities) >= 2:
                combined_named_entity = ' '.join(combined_entities)
                combined_named_entity = re.sub(r'\bנ\b', 'נגד', combined_named_entity)

                if combined_named_entity not in entity_set:
                    entities.append(combined_named_entity)

            if entities:
                enriched_query = query + " " + " ".join(entities)
            else:
                enriched_query = query

            return get_sparse_embedding(is_law, enriched_query)
        except Exception as e:
            logger.error(f"Error in get_sparse_embeddings: {e}")
            return []
