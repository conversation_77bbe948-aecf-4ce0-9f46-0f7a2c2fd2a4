import asyncio
import time
import traceback
from typing import List, Dict, Union

from pinecone.core.openapi.db_data.model.scored_vector import ScoredVector

from middlewares.logging_utils import app_logger as logger
from services.pre_chat.data_source import DataSource, PineConeQuery, VerdictChunkModel, MachshavotChunkModel, \
    LawChunkModel
from services.pre_chat.data_source.law_data_source import LawDataSource
from services.pre_chat.data_source.macshavot_data_source import MachshavotDataSource
from services.pre_chat.data_source.verdict_data_source import VerdictDataSource
from utils.pre_chat_dto import PreChatManager


class DataSourceAnalyzer:
    def __init__(self, request: PreChatManager):
        self.request = request

    async def search_old(self, query_list: List[PineConeQuery], total_query: int, not_filter=False) -> List[
        Union[VerdictChunkModel, MachshavotChunkModel, LawChunkModel]]:
        try:
            percentage = 100 / (total_query * 100)
            search_results = []
            data_source_list = DataSourceFactory.create_data_sources(self.request.domain, self.request, query_list)
            arr = await DataSourceFactory.run_source(data_source_list)

            for data_source in arr:
                ##Type DataSource Object
                ds_instance: DataSource = data_source['data_source']

                # TODO - calculate percentage
                temp_total_chunks = min(len(data_source['data']), int(percentage * 46))
                raw_list = data_source['data'][:temp_total_chunks]

                # Extract text and convert to BaseChunkModel objects VerdictChunkModel, MachshavotChunkModel, LawChunkModel
                structured_chunks = await ds_instance.text_extractor.parse_chunk(raw_list)
                search_results.extend(structured_chunks)

            if not search_results:
                # If no results found, raise a specific error
                logger.error(f"No search results found for query: {query_list}")
                raise ValueError("RESULTS_NONE_MESSAGE")

            return search_results

        except Exception as e:
            # Log the error with traceback
            logger.error(f"Error in DataSourceAnalyzer.search: {e},{traceback.print_exc()}")
            raise e

    async def search(
            self,
            query_list: List[PineConeQuery],
            total_query: int,
            not_filter: bool = False
    ) -> List[Union[VerdictChunkModel, MachshavotChunkModel, LawChunkModel]]:
        try:
            TARGET_CHUNKS = 46
            start_time = time.time()
            data_source_list = DataSourceFactory.create_data_sources(
                self.request.domain, self.request, query_list
            )
            if not data_source_list:
                logger.error(f"No data sources available for domain: {self.request.domain}")
                raise ValueError("RESULTS_NONE_MESSAGE")

            self.request.query.search_alpha = data_source_list[0].search_alpha
            raw_sources = await DataSourceFactory.run_source(data_source_list)
            all_raw_data = []

            # Equal allocation to all sources
            base_allocation = TARGET_CHUNKS // len(data_source_list)
            remainder = TARGET_CHUNKS % len(data_source_list)
            for i, data_source in enumerate(raw_sources):
                ds_instance: DataSource = data_source['data_source']
                raw_list = data_source['data']
                desired = base_allocation + (1 if i < remainder else 0)
                all_raw_data.append({
                    "ds_instance": ds_instance,
                    "raw_data": raw_list,
                    "available": len(raw_list),
                    "allocated": min(desired, len(raw_list)),
                })

            if not all_raw_data:
                logger.error(f"No data sources available for query: {query_list}")
                raise ValueError("RESULTS_NONE_MESSAGE")

            deficit = TARGET_CHUNKS - sum(item["allocated"] for item in all_raw_data)
            while deficit > 0:
                progress_made = False
                for item in all_raw_data:
                    extra_available = item["available"] - item["allocated"]
                    if extra_available > 0:
                        item["allocated"] += 1
                        deficit -= 1
                        progress_made = True
                        if deficit == 0:
                            break
                if not progress_made:
                    logger.warning(f"Unable to fully allocate {TARGET_CHUNKS} chunks, settling for less.")
                    break

            all_allocated_raw = []
            for item in all_raw_data:
                allocated_raw = item["raw_data"][:item["allocated"]]
                all_allocated_raw.extend(allocated_raw)

            first_extractor = all_raw_data[0]["ds_instance"].text_extractor
            search_results = await first_extractor.parse_chunk(all_allocated_raw)
            print(f"DataSourceAnalyzer took {time.time() - start_time:.2f} seconds")

            if len(search_results) < TARGET_CHUNKS:
                logger.warning(f"Only {len(search_results)} chunks returned (requested {TARGET_CHUNKS})")

            return search_results[:TARGET_CHUNKS]

        except Exception as e:
            logger.exception("Search failed", exc_info=e)
            raise


class DataSourceFactory:
    @staticmethod
    def create_data_source(domain: str, request: PreChatManager, query: PineConeQuery) -> DataSource:
        if domain == 'verdict':
            return VerdictDataSource(request, query)
        elif domain == 'machshavot':
            return MachshavotDataSource(request, query)
        elif domain == 'law':
            return LawDataSource(request, query)
        raise NotImplementedError

    @staticmethod
    def create_data_sources(domain: str, request: PreChatManager, queries: List[PineConeQuery]) -> List[DataSource]:
        data_sources = []
        for query in queries:
            data_source = DataSourceFactory.create_data_source(domain, request, query)
            data_sources.append(data_source)
        return data_sources

    @staticmethod
    async def run_source(data_source_list: List[DataSource]) -> List[Dict[str, Union[list[ScoredVector], DataSource]]]:
        tasks = [data_source.get_data(sort=False) for data_source in
                 data_source_list]
        # Run the tasks concurrently
        results = await asyncio.gather(*tasks)
        return [
            {'data': result, 'data_source': data_source_list[i]}
            for i, result in enumerate(results)
        ]
