import re
from io import BytesIO
from pathlib import Path
from docx.oxml.ns import qn

import mistune
from bs4 import BeautifulSoup
from docx import Document
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml import OxmlElement

from redis_db.redis_chat_manager import RedisChatManager
from redis_db.redis_schema import RedisSchema
from utils.conversation_manager import ConversationEnum
from utils.cache_db import get_redis

from middlewares.logging_utils import app_logger as logger
from utils.datetime_utils import timestamp_utc_to_jerusalem


def add_hyperlink(paragraph, url, text, color="959595"):
    """
    Add a hyperlink to a Word paragraph.

    :param paragraph: The paragraph to add the hyperlink to.
    :param url: The URL for the hyperlink.
    :param text: The display text for the hyperlink.
    :param color: The color of the hyperlink (default is blue).
    :param underline: Whether the hyperlink is underlined (default is True).
    """
    from docx import Document
    from docx.oxml import OxmlElement
    from docx.oxml.ns import qn
    part = paragraph.part
    r_id = part.relate_to(url, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",
                          is_external=True)

    hyperlink = OxmlElement("w:hyperlink")
    hyperlink.set(qn("r:id"), r_id)

    # Create the run for hyperlink text
    run = OxmlElement("w:r")
    rPr = OxmlElement("w:rPr")

    # Style the hyperlink
    if color:
        color_element = OxmlElement("w:color")
        color_element.set(qn("w:val"), color)
        rPr.append(color_element)
    bold_element = OxmlElement("w:b")
    bold_element.set(qn("w:val"), "1")
    rPr.append(bold_element)
    u = OxmlElement("w:u")
    u.set(qn("w:val"), "single")
    rPr.append(u)

    run.append(rPr)
    text_element = OxmlElement("w:t")
    text = text.replace("(", "").replace(")", "")
    text_element.text = text
    run.append(text_element)
    hyperlink.append(run)

    paragraph._p.append(hyperlink)


def fix_rtl_punctuation(text):
    """Add zero-width characters around punctuation in RTL text"""
    punctuation = '.,!?()[]{}:;'
    for punct in punctuation:
        text = text.replace(punct, '\u200f' + punct + '\u200f')
        text = re.sub(r'<br\s*/?>', '\n', text)  # \n for line breaks

    return text


def create_document(template_path):
    """Create and initialize document with proper styles"""
    doc = Document(template_path)
    # doc.styles['Normal'].paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    for style in doc.styles:
        try:
            if style.name != "Header_2" and style.name != "Footer_2":
                doc.styles[style.name].paragraph_format.rtl = True
                doc.styles[style.name].paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
            else:
                doc.styles[style.name].paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

        except:
            pass

    return doc


BASE_DIR = Path(__file__).resolve().parent


class ChatReport:

    def __init__(self, data_chat: RedisSchema, user_name):
        self.paragraph = None
        if not data_chat.conversation:
            raise ValueError("No conversation data found in the input.")

        # Initialize document processor
        doc_template = BASE_DIR / "template_techdin.docx"
        self.doc = create_document(doc_template)
        self.user_name = user_name

        # Process conversation
        self.conversation = data_chat.conversation
        self.citation = data_chat.citations if data_chat.citations else []
        self.last_element = None

    def _add_citations_block(self, message):
        citation_start = message.citations_index.start
        citation_end = message.citations_index.end


        if citation_start is not None and citation_end is not None:
            process_citations = False
            if self.citation and citation_start <= citation_end:
                if 0 <= (citation_start - 1) < len(self.citation):
                    process_citations = True

            if process_citations:
                citations_paragraph = self.doc.add_paragraph()
                try:
                    citations_paragraph.style = "paragraph_text"
                except KeyError:
                    logger.warning(
                        "Paragraph style 'paragraph_text' not found. Applying default RTL settings to 'Normal'.")
                    citations_paragraph.style = self.doc.styles['Normal']
                    citations_paragraph.paragraph_format.rtl = True
                    citations_paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT  # כפי שהיה בקוד שציינת שעובד

                citations_paragraph.paragraph_format.rtl = True
                citations_paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT  # כפי שהיה בקוד שציינת שעובד

                citations_paragraph.add_run("\n\n")
                mekorot_run = citations_paragraph.add_run("מקורות : ")
                mekorot_run.style = "character_heading_1"
                mekorot_run.bold = True

                rPr_mekorot = mekorot_run._r.get_or_add_rPr()
                if rPr_mekorot.find(qn('w:rtl')) is None:
                    rPr_mekorot.append(OxmlElement('w:rtl'))

                citations_processed_count = 0
                for i in range(citation_start - 1, citation_end):
                    if 0 <= i < len(self.citation):
                        citations_processed_count += 1
                        citation_item = self.citation[i]
                        citations_paragraph.add_run("\n\n")
                        ###add title
                        title_run = citations_paragraph.add_run(
                            fix_rtl_punctuation(citation_item["title"]))
                        title_run.style = "character_heading_2"
                        rPr_title = title_run._r.get_or_add_rPr()
                        if rPr_title.find(qn('w:rtl')) is None:
                            rPr_title.append(OxmlElement('w:rtl'))
                        citations_paragraph.add_run("\n")

                        cited_text_run = citations_paragraph.add_run(
                            fix_rtl_punctuation(citation_item["cited_text"]))
                        cited_text_run.style = "character_text"
                        rPr_cited_text = cited_text_run._r.get_or_add_rPr()
                        if rPr_cited_text.find(qn('w:rtl')) is None:
                            rPr_cited_text.append(OxmlElement('w:rtl'))
                        citations_paragraph.add_run("\n\n")
                    else:
                        logger.warning(
                            f"Citation index {i} (from 1-based start={citation_start}, end={citation_end}) is out of bounds for citation list of length {len(self.citation)}.")

                if citations_processed_count == 0:
                    logger.info(
                        f"Requested citation range {citation_start}-{citation_end} yielded no processable citations from the available {len(self.citation)}.")
            elif self.citation and not (
                    citation_start <= citation_end and 0 <= (citation_start - 1) < len(self.citation)):
                logger.warning(
                    f"Citation range {citation_start}-{citation_end} is invalid or out of bounds for the available {len(self.citation)} citations. Citations section will be skipped.")
            elif not self.citation:
                logger.info("No citations available in self.citation. Citations section will be skipped.")


    def set_rtl(self):
        """Set paragraph direction to RTL"""
        p = self.paragraph._p
        pPr = p.get_or_add_pPr()
        bidi = OxmlElement('w:bidi')
        pPr.append(bidi)
        for run in self.paragraph.runs:
            run._r.get_or_add_rPr().append(OxmlElement('w:rtl'))
        p.alignment = 0

    def order_list(self, element, elem_name):
        ## check if the size of the list is bigger than 1 and contain strong tag
        if len(element.find_all("li")) == 1:
            self.paragraph.add_run(fix_rtl_punctuation(element.get_text().strip()), style="character_heading_2")
            return None
        counter = 1
        for item in element:
            if len(item.get_text().strip().replace("\n", "").replace(" ", "")) == 0:
                continue
            self.paragraph.add_run(fix_rtl_punctuation(f"{counter}. {item.get_text().strip()}"), style="character_text")
            self.paragraph.add_run("\n")

            counter += 1

    def add_paragraph_with_style(self, element, style=None):
        """Add a paragraph with proper RTL formatting and style"""
        if not element or len(element.get_text().replace(" ", "").replace("\n", "")) == 0 or not element.name:
            return None
        elem_name = element.name
        self.paragraph.alignment = 0
        self.paragraph.paragraph_format.rtl = True
        self.paragraph.style = style

        if elem_name in ["ol", "ul"]:
            self.order_list(element, elem_name)
        elif elem_name and "h" in elem_name:
            style_heading = f"character_heading_2" if elem_name[1] in ["3", "4", "5", "6"] else "character_heading_1"
            self.paragraph.add_run(fix_rtl_punctuation(element.get_text().strip()), style=style_heading)

        ### for paragraph tag
        else:
            text = element.get_text(strip=True)
            try:
                if element.find('a'):
                    ## check if the paragraph contain hyperlink
                    link = element.find('a')  # Get the hyperlink URL
                    link_text = link.get_text(strip=True)  # Get the hyperlink text
                    link_hyperlink = link.get('href')  # Get the hyperlink URL
                    add_hyperlink(self.paragraph, link_hyperlink, link_text)

                    self.paragraph.add_run("\n")
                    text = text.replace(link_text, "")
            except Exception as e:
                logger.error(f"Error in Extract hyperlink: {e}")

            self.paragraph.add_run(fix_rtl_punctuation(text), style="character_text")
            self.paragraph.add_run("\n")

        if elem_name:
            self.last_element = elem_name
            self.paragraph.add_run("\n")
        self.set_rtl()

        # Get the current line spacing

    def run(self):
        """Generate chat report document"""
        # Process each message

        paragraph = self.doc.add_paragraph()
        paragraph.style = "Date"
        paragraph.alignment = 1
        timestamp = timestamp_utc_to_jerusalem(self.conversation[0].timestamp,string_foramt=True)
        paragraph.text = f'שיחה מיום :  {timestamp}'
        self.doc.styles['Date'].font.name = 'Arial'
        self.doc.styles['Date'].font.bold = True

        for message in self.conversation:
            self.last_element = None

            if message.action == ConversationEnum.MODEL_CONVERSATION:
                continue

            # Add writer name
            self.doc.styles['writer'].paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT

            writer_name = self.user_name if message.role == "user" else " AI טקדין "
            paragraph = self.doc.add_paragraph()
            paragraph.style = "writer"
            paragraph.alignment = 0
            paragraph.paragraph_format.rtl = True
            paragraph.text = writer_name

            # Process content

            markdown_parser = mistune.create_markdown()
            parsed_html = markdown_parser(message.content)

            soup = BeautifulSoup(parsed_html, "html.parser")
            style = "paragraph_text"
            # Iterate through HTML elements and add them to the Word document
            self.paragraph = self.doc.add_paragraph()

            for element in soup.contents:
                self.add_paragraph_with_style(element, style)

            if message.role == "assistant":
                # Add citations block if available
                self._add_citations_block(message)
        self.doc.save("output2.docx")

        return self.doc


async def download_chat(chat_id):
    redis_conn = await get_redis()
    chat = await redis_conn.get(chat_id)

    return chat


async def main():
    import json
    chat_id = "9a636ad2-5ef7-4dc2-aa64-478fc90b6fed"
    redis_pool = await get_redis()
    _, chat_session = await RedisChatManager(redis_pool).load_chat_from_redis(chat_id, "", "")

    # chat = await download_chat(chat_id)
    # for message in conversation_json["conversation"]:
    #     print(message["content"])
    ChatReport(chat_session, "Gal").run()


if __name__ == '__main__':
    import asyncio

    asyncio.run(main())
