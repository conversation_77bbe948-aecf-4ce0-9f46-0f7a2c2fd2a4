import json
import traceback
from time import time
from typing import List

import configs.app_config as conf
from redis_db.redis_chat_manager import RedisChatManager
from utils.conversation_manager import <PERSON>Enum, ConversationEnum, ConversationItem
from utils.dto import ActionsEnum, SearchMultipleIndicesRequest, ListItem, ListItemSample
from utils.dto import SearchTypeEnum, CollectionNameEnum, \
    ExtractTextEmbeddingReuquest
from services.post_chat.post_chat_dto import ReturnRelatedChunksRequest, PostChatRelatedRequest, PostChatAnalytics, \
    convert_list_items_to_chunks_context
from app.middlewares.exceptions import get_value_from_error_messages_json
from utils.analytics import create_document, get_search_analytics
from utils.pinecone_utli import ListItemEncoder, convert_search_results_to_list_items
from data_ops.embedding_helper import extract_text_embedding
from middlewares.logging_utils import app_logger as logger
from middlewares.logging_utils import log_format
from db_utils.pinecone_helper import sort_items
from data_ops.pinecone_func import search_query_in_multiple_indices, update_verdicts_in_search_results


async def post_chat_related(request: PostChatRelatedRequest) -> List[ListItemSample]:
    from datetime import datetime

    chat_id = request.chat_id
    user_id = request.user_id
    redis_pool = request.redis_pool
    cohere_client = request.cohere_client
    law_index = request.law_index
    verdict_index = request.verdict_index
    books_summaries_index = request.books_summaries_index
    query_statistics_results = request.query_statistics_results
    domain = request.domain
    ai_provider = request.ai_provider
    txtId = None

    try:
        max_results = 50
        _, chat_data = await RedisChatManager(redis_pool).load_chat_from_redis(chat_id, user_id, "")

        query = chat_data.query
        answer = chat_data.answer

        last_q_and_a = f"{query} {answer}"

        extract_context_embedding_for_first_question_request = ExtractTextEmbeddingReuquest(chat_id=chat_id,
                                                                                            user_id=user_id,
                                                                                            cohere_client=cohere_client,
                                                                                            query_statistics_results=query_statistics_results,
                                                                                            text_to_embed=last_q_and_a)
        embedding = await extract_text_embedding(extract_context_embedding_for_first_question_request)

        return_related_chunks_request = ReturnRelatedChunksRequest(
            user_id=user_id,
            chat_id=chat_id,
            last_q_and_a=last_q_and_a,
            cohere_client=cohere_client,
            law_index=law_index,
            verdict_index=verdict_index,
            books_summaries_index=books_summaries_index,
            max_results=max_results,
            embedding=embedding,
            ai_provider=ai_provider)
        related_and_refferer = await return_related_chunks_by_last_question_and_answer(
            return_related_chunks_request)

        try:
            pca = PostChatAnalytics(user_id=user_id,
                                    chat_id=chat_id,
                                    chunks_context=convert_list_items_to_chunks_context(related_and_refferer),
                                    query_index=chat_data.chat_settings.session_index,
                                    search_alpha=0.5)

            create_document("post_chat_analytics", pca.model_dump())
        except Exception as e:
            logger.warning(f'PostChatAnalytics error - {e}')

        return related_and_refferer
    except Exception as e:
        print(traceback.print_exc())
        logger.error(f'Failed to get related and refferer for chat_id: {chat_id}, error: {e} , {traceback.print_exc()}')
        raise Exception(get_value_from_error_messages_json(str(e)) if str(
            e) in conf.ERROR_KEYS else get_value_from_error_messages_json('REL_REF_ERROR')) from e


async def return_related_chunks_by_last_question_and_answer(request: ReturnRelatedChunksRequest) -> List[
    ListItemSample]:
    search_analytics_post_chat = request.search_analytics_post_chat
    user_id = request.user_id
    chat_id = request.chat_id
    last_q_and_a = request.last_q_and_a
    cohere_client = request.cohere_client
    law_index = request.law_index
    verdict_index = request.verdict_index
    books_summaries_index = request.books_summaries_index
    max_results = int(request.max_results)
    embedding = request.embedding
    ai_provider = request.ai_provider
    #####TODO ADD FILTER filters = [{{"court_name":{"$nin":"שלום"}}}]

    try:
        t0 = time()
        start = time()
        search_request: SearchMultipleIndicesRequest = {"query": last_q_and_a,
                                                        "cohere_client": cohere_client,
                                                        "law_index": law_index,
                                                        "verdict_index": verdict_index,
                                                        "books_summaries_index": books_summaries_index,
                                                        "max_results": max_results,
                                                        "filters": [],
                                                        "consolidate_source_docs": True,
                                                        "search_analytics": search_analytics_post_chat,
                                                        "embeddings": embedding,
                                                        "user_id": user_id,
                                                        "chat_id": chat_id,
                                                        "ai_provider": ai_provider
                                                        # "domain":domain
                                                        }
        related_results, ner_time = await search_query_in_multiple_indices(search_request)
        update_verdicts_in_search_results(related_results)
        sorted_related_results = sort_items(related_results)
        converted_related_results = convert_search_results_to_list_items(sorted_related_results)

        logger.info(log_format(
            {'userId': user_id, 'Type': ActionsEnum.query, 'Action': 'related_results', 'id': chat_id,
             'Time': time() - t0, 'Data': '', 'text': 'update related', 'numbering': 9}))
        logger.info(log_format({'userId': user_id, 'Type': ActionsEnum.chat, 'Action': 'call to sql', 'id': chat_id,
                                'Time': time() - start, 'Data': '', 'text': 'system: update text from sql ',
                                'numbering': 13}))

        return converted_related_results
    except Exception as e:
        logger.error(f'Failed to get related chunks for chat_id: {chat_id}, error: {e}')
        raise Exception(str(e) if str(e) in conf.ERROR_KEYS else 'RELATED_CHUNKS_ERROR') from e
